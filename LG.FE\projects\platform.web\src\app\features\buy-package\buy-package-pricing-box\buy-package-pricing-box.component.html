<!-- Modern Marketing-Friendly Pricing Card -->
<div class="modern-pricing-card" [class.selected]="selected" [class.most-popular]="index === 1" [class.best-value]="index === 2">

    <!-- Top Badges -->
    <div class="card-badges">
        <!-- Discount Badge -->
        @if (package.numberOfLessonsPerPackageDiscountPercentage > 0) {
            <div class="discount-badge">
                <i class="pi pi-tag"></i>
                <span>Save {{package.numberOfLessonsPerPackageDiscountPercentage}}%</span>
            </div>
        }

        <!-- Marketing Badges -->
        @if (index === 1) {
            <div class="marketing-badge popular">
                <i class="pi pi-star-fill"></i>
                <span>Most Popular</span>
            </div>
        } @else if (index === 2) {
            <div class="marketing-badge best-value">
                <i class="pi pi-crown"></i>
                <span>Best Value</span>
            </div>
        }
    </div>
    <!-- Main Card Content -->
    <div class="card-content">

        <!-- Header Section -->
        <div class="card-header">
            <div class="package-title">
                <h3>{{package.numberOfLessons}} Lessons</h3>
                <p class="language-info">{{package.teachingLanguageName}}</p>
            </div>

            <!-- Duration Display -->
            @if (duration > 0) {
                <div class="duration-display">
                    <span class="duration-value">{{duration}} min</span>
                    <span class="duration-label">per lesson</span>
                </div>
            }
        </div>

        <!-- Pricing Section -->
        <div class="pricing-section">
            @if (numberOfStudents > 1) {
                <div class="price-display">
                    <span class="price-amount">{{package.pricePerLesson}}€</span>
                    <span class="price-label">per lesson</span>
                </div>
                <!-- @if (package.groupWithoutDiscountPrice && package.groupWithoutDiscountPrice > package.pricePerLesson) {
                    <div class="original-price">
                        <span class="strikethrough">{{package.groupWithoutDiscountPrice}}€</span>
                    </div>
                } -->
            } @else {
                <div class="price-display">
                    <span class="price-amount">{{package.oneToOnePricePerLesson}}€</span>
                    <span class="price-label">per lesson</span>
                </div>
            }
        </div>

        <!-- Features Section -->
        <div class="features-section">
            <ul class="features-list">
                <li class="feature-item">
                    <i class="pi pi-check-circle"></i>
                    <span>Personalized lessons tailored to your goals</span>
                </li>
                <li class="feature-item">
                    <i class="pi pi-check-circle"></i>
                    <span>Free learning materials to boost your progress</span>
                </li>
                @if (package.numberOfLessons > 10) {
                    <li class="feature-item premium">
                        <i class="pi pi-star-fill"></i>
                        <span>Priority Customer Support</span>
                    </li>
                }
            </ul>
        </div>

        <!-- Total Price Section -->
        <div class="total-price-section">
            <div class="total-price">
                <span class="total-label">Total Package</span>
                <span class="total-amount">{{package.finalPrice}}€</span>
            </div>
            <div class="validity-info">
                <span>Valid for {{package.validityInMonths}} months</span>
            </div>
        </div>

        <!-- What's Included Link -->
        <div class="included-link">
            <a (click)="openIncludedTextDialog($event)">
                <i class="pi pi-info-circle"></i>
                <span>What's included?</span>
            </a>
        </div>

        <!-- Modern Extension Toggle -->
        <div class="extension-section">
            <div class="extension-toggle" [class.active]="extensionSelected" (click)="toggleExtension($event)">
                <div class="extension-content">
                    <div class="extension-info">
                        <i class="pi pi-plus-circle"></i>
                        <span class="extension-label">Add Package Extension</span>
                    </div>
                    <div class="extension-price">
                        <span class="price-value">+20€</span>
                    </div>
                </div>
                <div class="extension-checkbox">
                    <i class="pi" [class.pi-check]="extensionSelected" [class.pi-plus]="!extensionSelected"></i>
                </div>
            </div>
        </div>

        <!-- Modern Add to Cart Button -->
        <div class="cart-section">
            <button class="add-to-cart-btn"
                    [class.added]="selected"
                    [class.success-message]="showSuccessMessage()"
                    [class.loading-message]="showLoadingMessage()"
                    [disabled]="showLoadingMessage()"
                    (click)="onAddToCart($event)">

                <!-- Loading Message State -->
                @if (showLoadingMessage()) {
                    <div class="btn-loading-content">
                        <i class="pi pi-spin pi-spinner loading-icon"></i>
                        <span class="loading-text">Adding to Cart...</span>
                        <div class="loading-animation"></div>
                    </div>
                } @else if (showSuccessMessage()) {
                    <!-- Success Message State -->
                    <div class="btn-success-content">
                        <i class="pi pi-check-circle success-icon"></i>
                        <span class="success-text">Added to Cart!</span>
                        <div class="success-animation"></div>
                    </div>
                } @else {
                    <!-- Normal Button State -->
                    <div class="btn-content">
                        <i class="pi" [class.pi-cart-plus]="!selected" [class.pi-check]="selected"></i>
                        <span>{{selected ? 'Added to Cart' : 'Add to Cart'}}</span>
                    </div>
                    @if (!selected) {
                        <div class="btn-hover-effect">
                            <span>Choose This Package</span>
                        </div>
                    }
                }
            </button>
        </div>
    </div>
</div>