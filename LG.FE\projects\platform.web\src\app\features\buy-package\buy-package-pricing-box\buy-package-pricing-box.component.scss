@use "mixins";

:host {
  width: 100%;
  display: block;

  // Modern Pricing Card Variables
  --pricing-primary: #6366f1;
  --pricing-secondary: #8b5cf6;
  --pricing-success: #10b981;
  --pricing-warning: #f59e0b;
  --pricing-error: #ef4444;
  --pricing-bg: rgba(255, 255, 255, 0.98);
  --pricing-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --pricing-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --pricing-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --pricing-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --pricing-border-radius: 16px;
}

// Modern Pricing Card
.modern-pricing-card {
  position: relative;
  width: 100%;
  height: 100%;
  transition: var(--pricing-transition);

  @include mixins.breakpoint(mobile) {
    margin-bottom: 1rem;
  }

  &:hover {
    transform: translateY(-4px);

    .card-content {
      box-shadow: var(--pricing-shadow-xl);
    }
  }

  &.selected {
    transform: translateY(-2px);

    .card-content {
      border-color: var(--pricing-primary);
      box-shadow: var(--pricing-shadow-xl);
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
    }
  }

  &.most-popular {
    .card-content {
      border-color: rgba(34, 197, 94, 0.4);

      &::before {
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.03) 0%, rgba(34, 197, 94, 0.02) 100%);
        opacity: 1;
      }
    }
  }

  &.best-value {
    .card-content {
      border-color: rgba(139, 92, 246, 0.4);

      &::before {
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.03) 0%, rgba(139, 92, 246, 0.02) 100%);
        opacity: 1;
      }
    }
  }
}

// Card Badges - Responsive & Non-Overlapping
.card-badges {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-end;
  max-width: 40%;

  @include mixins.breakpoint(tablet) {
    top: 0.625rem;
    right: 0.625rem;
    gap: 0.2rem;
    max-width: 45%;
  }

  @include mixins.breakpoint(mobile) {
    position: static;
    max-width: none;
    flex-direction: row;
    justify-content: center;
    margin-bottom: 0.75rem;
    gap: 0.375rem;
    order: -1;
  }

  .discount-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.625rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(8px);
    animation: discountPulse 2.5s ease-in-out infinite;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    flex-shrink: 0;
    pointer-events: none;

    @include mixins.breakpoint(tablet) {
      padding: 0.3rem 0.5rem;
      font-size: 0.65rem;
      gap: 0.2rem;
      border-radius: 12px;
    }

    @include mixins.breakpoint(mobile) {
      padding: 0.25rem 0.4rem;
      font-size: 0.6rem;
      gap: 0.15rem;
      border-radius: 10px;
      letter-spacing: 0.02em;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
      transition: left 0.6s;
    }

    &:hover::before {
      left: 100%;
    }

    i {
      font-size: 0.65rem;
      filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
      z-index: 1;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6rem;
      }
    }

    span {
      z-index: 1;
      position: relative;
    }
  }

  .marketing-badge {
    display: flex;
    align-items: center;
    gap: 0.2rem;
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.02em;
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(5px);
    position: relative;
    transition: var(--pricing-transition);
    white-space: nowrap;
    flex-shrink: 0;
    pointer-events: none;

    @include mixins.breakpoint(tablet) {
      padding: 0.2rem 0.4rem;
      font-size: 0.55rem;
      gap: 0.15rem;
      border-radius: 6px;
    }

    @include mixins.breakpoint(mobile) {
      padding: 0.15rem 0.35rem;
      font-size: 0.5rem;
      gap: 0.1rem;
      border-radius: 5px;
      letter-spacing: 0.01em;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    &.popular {
      background: rgba(34, 197, 94, 0.9);
      color: white;
      border-color: rgba(34, 197, 94, 0.3);
      box-shadow: 0 1px 3px rgba(34, 197, 94, 0.2);
    }

    &.best-value {
      background: rgba(139, 92, 246, 0.9);
      color: white;
      border-color: rgba(139, 92, 246, 0.3);
      box-shadow: 0 1px 3px rgba(139, 92, 246, 0.2);
    }

    i {
      font-size: 0.55rem;
      opacity: 0.9;

      @include mixins.breakpoint(mobile) {
        font-size: 0.5rem;
      }
    }

    span {
      line-height: 1;
    }
  }
}

// Card Content
.card-content {
  background: var(--pricing-bg);
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: var(--pricing-border-radius);
  padding: 1.25rem;
  height: 100%;
  transition: var(--pricing-transition);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;

  @include mixins.breakpoint(mobile) {
    padding: 1rem;
  }


  &:hover::before {
    opacity: 1;
  }
}

// Card Header
.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 1.25rem;
  position: relative;
  z-index: 2;

  @include mixins.breakpoint(mobile) {
    margin-bottom: 1rem;
    // Ensure proper spacing when badges are moved above on mobile
    margin-top: 0.5rem;
  }

  .package-title {
    margin-bottom: 0.75rem;

    h3 {
      margin: 0 0 0.25rem 0;
      font-size: 1.125rem;
      font-weight: 800;
      color: #1f2937;
      line-height: 1.2;

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
      }
    }

    .language-info {
      margin: 0;
      font-size: 0.875rem;
      color: var(--pricing-primary);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.025em;
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.08) 100%);
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      border: 1px solid rgba(99, 102, 241, 0.15);
      display: inline-block;
      margin-top: 0.375rem;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8rem;
        padding: 0.2rem 0.4rem;
        margin-top: 0.3rem;
      }
    }
  }

  .duration-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.375rem 0.625rem;
    background: rgba(99, 102, 241, 0.1);
    border-radius: 6px;
    min-width: 3.5rem;

    .duration-value {
      font-size: 0.75rem;
      font-weight: 700;
      color: var(--pricing-primary);
      line-height: 1;
    }

    .duration-label {
      font-size: 0.625rem;
      color: #6b7280;
      line-height: 1;
    }
  }
}

// Pricing Section
.pricing-section {
  text-align: center;
  margin-bottom: 1.25rem;
  position: relative;
  z-index: 2;

  @include mixins.breakpoint(mobile) {
    margin-bottom: 1rem;
  }

  .price-display {
    margin-bottom: 0.375rem;

    .price-amount {
      font-size: 2rem;
      font-weight: 800;
      color: var(--pricing-primary);
      line-height: 1;

      @include mixins.breakpoint(mobile) {
        font-size: 1.875rem;
      }
    }

    .price-label {
      display: block;
      font-size: 0.75rem;
      color: #6b7280;
      font-weight: 500;
      margin-top: 0.25rem;
    }
  }

  .original-price {
    .strikethrough {
      font-size: 0.875rem;
      color: #9ca3af;
      text-decoration: line-through;
      font-weight: 500;
    }
  }
}

// Features Section
.features-section {
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
  flex: 1;
  display: flex;
  flex-direction: column;

  @include mixins.breakpoint(mobile) {
    margin-bottom: 0.875rem;
  }

  .features-list {
    list-style: none;
    padding: 0;
    margin: 0;
    min-height: 4rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    @include mixins.breakpoint(mobile) {
      min-height: 4rem;
    }

    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
      font-size: 0.75rem;
      color: #374151;
      line-height: 1.3;
      text-align: left;

      @include mixins.breakpoint(mobile) {
        font-size: 0.7rem;
        margin-bottom: 0.375rem;
        gap: 0.375rem;
      }

      &:last-child {
        margin-bottom: 0;
      }

      i {
        font-size: 0.875rem;
        color: var(--pricing-success);
        flex-shrink: 0;
        margin-top: 0.125rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }

      &.premium {
        i {
          color: var(--pricing-warning);
        }

        span {
          font-weight: 600;
          color: #1f2937;
        }
      }

      span {
        font-weight: 500;
        flex: 1;
      }
    }
  }
}

// Total Price Section - Clean & Intuitive
.total-price-section {
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 2;

  @include mixins.breakpoint(mobile) {
    margin-bottom: 0.625rem;
  }

  .total-price {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    padding: 0.625rem 0.75rem;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(139, 92, 246, 0.06) 100%);
    border-radius: 8px;
    border: 1px solid rgba(99, 102, 241, 0.15);
    margin-bottom: 0.375rem;

    @include mixins.breakpoint(mobile) {
      padding: 0.5rem 0.625rem;
    }

    .total-label {
      font-size: 0.7rem;
      color: #6b7280;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.025em;

      @include mixins.breakpoint(mobile) {
        font-size: 0.65rem;
      }
    }

    .total-amount {
      font-size: 1.125rem;
      font-weight: 800;
      color: var(--pricing-primary);
      line-height: 1;

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
      }
    }
  }

  .validity-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    padding: 0.375rem 0.625rem;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.06) 0%, rgba(139, 92, 246, 0.04) 100%);
    border: 1px solid rgba(99, 102, 241, 0.12);
    border-radius: 6px;

    @include mixins.breakpoint(mobile) {
      padding: 0.3rem 0.5rem;
      gap: 0.3rem;
    }

    &::before {
      content: '';
      width: 0.75rem;
      height: 0.75rem;
      background: var(--pricing-primary);
      border-radius: 50%;
      flex-shrink: 0;
      opacity: 0.7;

      @include mixins.breakpoint(mobile) {
        width: 0.65rem;
        height: 0.65rem;
      }
    }

    span {
      font-size: 0.65rem;
      color: #4b5563;
      font-weight: 600;
      line-height: 1;
      text-transform: uppercase;
      letter-spacing: 0.025em;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6rem;
      }
    }
  }
}

// Included Link
.included-link {
  text-align: center;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;

  @include mixins.breakpoint(mobile) {
    margin-bottom: 0.875rem;
  }

  a {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--pricing-primary);
    text-decoration: none;
    font-size: 0.7rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--pricing-transition);

    &:hover {
      color: var(--pricing-secondary);
      transform: translateY(-1px);
    }

    i {
      font-size: 0.65rem;
    }
  }
}

// Extension Section
.extension-section {
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;

  @include mixins.breakpoint(mobile) {
    margin-bottom: 0.875rem;
  }

  .extension-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.625rem 0.75rem;
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 10px;
    cursor: pointer;
    transition: var(--pricing-transition);

    @include mixins.breakpoint(mobile) {
      padding: 0.5rem 0.625rem;
    }

    &:hover {
      border-color: rgba(99, 102, 241, 0.3);
      background: rgba(99, 102, 241, 0.02);
    }

    &.active {
      border-color: var(--pricing-primary);
      background: rgba(99, 102, 241, 0.05);

      .extension-checkbox {
        background: var(--pricing-primary);
        color: white;
      }

      .extension-price {
        color: var(--pricing-primary);
      }
    }

    .extension-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      margin-right: 0.75rem;

      .extension-info {
        display: flex;
        align-items: center;
        gap: 0.375rem;

        i {
          font-size: 0.875rem;
          color: var(--pricing-primary);
        }

        .extension-label {
          font-size: 0.75rem;
          font-weight: 600;
          color: #374151;

          @include mixins.breakpoint(mobile) {
            font-size: 0.7rem;
          }
        }
      }

      .extension-price {
        .price-value {
          font-size: 0.75rem;
          font-weight: 700;
          color: var(--pricing-success);
        }
      }
    }

    .extension-checkbox {
      width: 1.25rem;
      height: 1.25rem;
      background: rgba(226, 232, 240, 0.6);
      border-radius: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: var(--pricing-transition);

      i {
        font-size: 0.75rem;
      }
    }
  }
}

// Cart Section
.cart-section {
  margin-top: auto;
  position: relative;
  z-index: 2;

  .add-to-cart-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--deep-lavender);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 0.875rem;
    font-weight: 700;
    cursor: pointer;
    transition: var(--pricing-transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--pricing-shadow);

    @include mixins.breakpoint(mobile) {
      padding: 0.625rem 0.875rem;
      font-size: 0.8rem;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--pricing-shadow-lg);

      // .btn-hover-effect {
      //   opacity: 1;
      //   transform: translateY(0);
      // }

      // .btn-content {
      //   opacity: 0;
      //   transform: translateY(-100%);
      // }
    }

    &.added {
      background: linear-gradient(135deg, var(--pricing-success) 0%, #059669 100%);

      &:hover {
        transform: none;
        box-shadow: var(--pricing-shadow);
      }
    }

    &.success-message {
      background: linear-gradient(135deg, var(--pricing-success) 0%, #059669 100%);
      // transform: scale(1.02);
      box-shadow: var(--pricing-shadow-xl);

      &:hover {
        // transform: scale(1.02);
        box-shadow: var(--pricing-shadow-xl);

        .btn-hover-effect {
          opacity: 0;
        }

        .btn-content {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .btn-success-content {
        animation: successPulse 1s ease-out;
      }
    }

    &.loading-message {
      background: linear-gradient(135deg, var(--pricing-primary) 0%, var(--pricing-secondary) 100%);
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: var(--pricing-shadow);

        .btn-hover-effect {
          opacity: 0;
        }

        .btn-content {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .btn-loading-content {
        animation: loadingPulse 1.5s ease-in-out infinite;
      }
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.9;
    }

    .btn-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.375rem;
      transition: var(--pricing-transition);

      i {
        font-size: 0.875rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }
    }

    .btn-loading-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      position: relative;
      z-index: 3;

      .loading-icon {
        font-size: 1rem;
        color: white;

        @include mixins.breakpoint(mobile) {
          font-size: 0.9rem;
        }
      }

      .loading-text {
        font-weight: 700;
        font-size: 0.875rem;
        letter-spacing: 0.5px;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }

      .loading-animation {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 80px;
        height: 80px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%) scale(0);
        animation: loadingRipple 2s ease-in-out infinite;
        pointer-events: none;

        @include mixins.breakpoint(mobile) {
          width: 60px;
          height: 60px;
        }
      }
    }

    .btn-success-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      position: relative;
      z-index: 3;

      .success-icon {
        font-size: 1rem;
        color: white;
        animation: successIconBounce 0.6s ease-out;

        @include mixins.breakpoint(mobile) {
          font-size: 0.9rem;
        }
      }

      .success-text {
        font-weight: 700;
        font-size: 0.875rem;
        letter-spacing: 0.5px;
        text-transform: uppercase;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }

      .success-animation {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 80px;
        height: 80px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%) scale(0);
        animation: successRipple 1s ease-out;
        pointer-events: none;

        @include mixins.breakpoint(mobile) {
          width: 60px;
          height: 60px;
        }
      }
    }

    .btn-hover-effect {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, var(--pricing-secondary) 0%, var(--pricing-primary) 100%);
      opacity: 0;
      transform: translateY(100%);
      transition: var(--pricing-transition);

      span {
        font-weight: 700;
        font-size: 0.875rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }
    }
  }
}

// Enhanced Animations
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes discountPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  }
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes sparkle {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: rotate(180deg) scale(1);
    opacity: 1;
  }
  75% {
    transform: rotate(270deg) scale(1.1);
    opacity: 0.8;
  }
}

// Success Message Animations
@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes successIconBounce {
  0% {
    transform: scale(0.3) rotate(-45deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes successRipple {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

// Loading Message Animations
@keyframes loadingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

@keyframes loadingRipple {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}