@if (mini()) {
  <li class="modern-lesson-mini">
    <div class="lesson-mini-content">
      <div class="mini-header">
        <div class="mini-lesson-info">
          <div class="mini-icon">
            @if (lesson().lesson) {
              <img [src]="generalService.getImageUrlForLanguage(lesson().lesson?.teachingLanguageName!)"
                   width="20" height="20" alt="Language Flag" class="border-round"
                   [pTooltip]="lesson().lesson?.teachingLanguageName!" tooltipPosition="top" />
            } @else {
              <i class="pi pi-calendar"></i>
            }
          </div>
          <div class="mini-details">
            <div class="mini-title">{{ lessonTitle() }}</div>
            <div class="mini-meta">
              {{ formattedDate() }} | {{ formattedStartTime() }} - {{ formattedEndTime() }}
            </div>
          </div>
        </div>
        <div class="mini-status">
          <div class="modern-status-badge" [ngClass]="getModernStatusClass()">
            <div class="status-dot"></div>
            <span>{{ statusText() }}</span>
          </div>
        </div>
      </div>
    </div>
  </li>
}@else {

  <div class="modern-lesson-card border-1 surface-card surface-border">
    <!-- Card Header -->
    <div class="lesson-card-header">
      <div class="lesson-title-section">
        <div class="lesson-info">
          @if (lesson().lesson) {
            @if (lesson().lesson?.teachingLanguageName) {
            <img [src]="generalService.getImageUrlForLanguage(lesson().lesson.teachingLanguageName)"
                 alt="Language Flag" class="language-flag"
                 [pTooltip]="lesson().lesson.teachingLanguageName" tooltipPosition="top" />
            }
            <div class="lesson-title">{{ lessonTitle() }}</div>
          } @else {
            <div class="lesson-icon">
              <i class="pi pi-calendar"></i>
            </div>
            <div class="lesson-title">Test Lesson Slot</div>
          }
        </div>
        <!-- <div class="modern-status-badge" [ngClass]="getModernStatusClass()">
          <div class="status-dot"></div>
          <span>{{ statusText() }}</span>
        </div> -->
      </div>

      <div class="lesson-meta">
        <div class="meta-row">
          <i class="pi pi-calendar"></i>
          <span>{{ this.timezoneService.convertUtcToLocal(this.lesson().startDateTime, {
            timezone: this.timezoneService.getTimezone(), outputFormat: 'string', formatPattern: 'MMM D, y'
          }) }}</span>
        </div>
        <div class="meta-row">
          <i class="pi pi-clock"></i>
          <span>{{ this.timezoneService.convertUtcToLocal(this.lesson().startDateTime, {
            timezone: this.timezoneService.getTimezone(), outputFormat: 'string', formatPattern: 'HH:mm'
          }) }} - {{ this.timezoneService.convertUtcToLocal(this.lesson().endDateTime, {
            timezone: this.timezoneService.getTimezone(), outputFormat: 'string', formatPattern: 'HH:mm'
          }) }}</span>
          <div class="duration-badge">{{ lessonDuration() }}</div>
        </div>
      </div>
    </div>
  
    <hr class="w-full my-1 border-gray-100 border-top-1">
  
    <!-- @if (lesson().lesson?.teacher) {
    <div class="flex align-items-start mb-2">
      <div>
        <span class="block text-900 mb-1 text-base font-medium">
          <i class="pi pi-user-edit mr-2"></i>{{ getParticipantFullName(lesson().lesson?.teacher) }}
        </span>
        <p class="text-600 mt-0 mb-0 text-xs">{{ lesson().lesson?.teacher?.timeZoneDisplayName! }}</p>
      </div>
    </div>
    } -->
  
    <div class="flex justify-content-between p-2 gap-3">
      @if (lesson().lesson) {
      @if (lesson().lesson?.lessonId) {
      <p-button (click)="generalService.goToLessonDetails(lesson().lesson.lessonId)" label="Overview" icon="pi pi-eye"
        class="w-full" styleClass="btn-soft-success w-full text-base py-2">
      </p-button>
      }

      @if (lesson().lesson?.lessonStatus === LessonStatus.Scheduled) {
      <p-button label="Join" icon="pi pi-sign-in" class="w-full" styleClass="submit-btn w-full text-base py-2">
      </p-button>
      }
      } @else {
      <!-- <p-button label="Book Lesson" icon="pi pi-calendar-plus" class="w-full"
        styleClass="submit-btn w-full text-base py-2">
      </p-button> -->
      }
    </div>
  </div>
}

  <p-menu #menu1 appendTo="body" [popup]="true" [model]="items"></p-menu>