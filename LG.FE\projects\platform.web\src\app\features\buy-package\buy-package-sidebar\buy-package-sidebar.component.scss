@use "mixins";

:host {
  display: block;
  height: 100%;

  // Modern Checkout Variables

  @media (min-width:1200px) and (min-height:900px) {
    position: -webkit-sticky;
    /* For Safari */
    position: sticky;
    top: 90px;
  }

}

.cart-sidebar {
  // background-color: #fbfbfb;

  &.inSidebar {
    background-image: none;
    background-color: #fff;
  }

}

.offcanvas-body {
  flex-grow: 1;
  overflow-y: auto;
}

.inSidebar {

  border-radius: 0 !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.inCheckout {
  position: fixed;
  height: calc(100vh - 75px);
  right: 0;
  width: 320px;
}


.saving-bg {
  background-image: linear-gradient(90deg,
      hsl(216deg 91% 65%) 0%,
      hsl(214deg 92% 66%) 11%,
      hsl(212deg 93% 66%) 22%,
      hsl(210deg 93% 67%) 33%,
      hsl(209deg 93% 68%) 44%,
      hsl(208deg 92% 69%) 56%,
      hsl(207deg 92% 71%) 67%,
      hsl(206deg 90% 72%) 78%,
      hsl(205deg 89% 74%) 89%,
      hsl(204deg 87% 75%) 100%);
}

:host ::ng-deep {
  .button_addon button {
    padding: 0.3rem 0.5rem !important;
    line-height: 1;
    color: var(--deep-lavender);
  }

  .remove-btn {
    background-color: #EB6777;
    border-color: #EB6777;
  }

  .promo_button button {
    background-color: #9992EC;
    border-color: #9992EC;
  }

  .q-btn {
    padding: 0.2rem;
    background-color: white;
    color: #333;
    border: 1px solid #673AB7;
  }
}

// Modern Order Summary
.modern-order-summary {
  margin-top: 1.5rem;
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  position: relative;
  // overflow: hidden;

  @include mixins.breakpoint(mobile) {
    margin-top: 1.25rem;
    padding: 1rem;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
    pointer-events: none;
  }

  .summary-content {
    position: relative;
    z-index: 2;

    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(226, 232, 240, 0.4);
      transition: var(--checkout-transition);

      @include mixins.breakpoint(mobile) {
        padding: 0.375rem 0;
      }

      &:last-of-type {
        border-bottom: none;
        margin-bottom: 0.75rem;
      }

      &.discount-row {
        .summary-label {
          display: flex;
          align-items: center;
          gap: 0.375rem;
          color: var(--checkout-success);

          i {
            font-size: 0.8rem;
          }
        }

        .discount-value {
          color: var(--checkout-success);
          font-weight: 600;
        }
      }

      .summary-label {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }

      .summary-value {
        font-size: 0.875rem;
        color: #374151;
        font-weight: 600;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }
    }

    .summary-total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0 0 0;
      border-top: 2px solid rgba(99, 102, 241, 0.1);
      // margin-top: 0.75rem;

      @include mixins.breakpoint(mobile) {
        padding: 0.625rem 0 0 0;
        margin-top: 0.625rem;
      }

      .total-label {
        font-size: 1rem;
        font-weight: 700;
        color: #1f2937;

        @include mixins.breakpoint(mobile) {
          font-size: 0.9rem;
        }
      }

      .total-value {
        font-size: 1.125rem;
        font-weight: 800;
        color: var(--deep-lavender);

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
        }
      }
    }
  }
}

// Modern Action Buttons
.modern-checkout-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1.25rem;

  @include mixins.breakpoint(mobile) {
    gap: 0.625rem;
    margin-top: 1rem;
  }
}

.modern-payment-action {
  margin-top: 1rem;

  @include mixins.breakpoint(mobile) {
    margin-top: 0.875rem;
  }
}

// Modern Button Styles
