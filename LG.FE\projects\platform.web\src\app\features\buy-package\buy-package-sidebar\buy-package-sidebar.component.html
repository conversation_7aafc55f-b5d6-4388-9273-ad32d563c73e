<div class="cart-sidebar p-2 sm:p-3 overflow-y-auto text" [ngClass]="{
    'inSidebar': inSidebar(),
  'border-noround	': inSidebar(),
  'inCheckout	': inCheckout(),
  'text-800	': editMode()}">

  @if (inSidebar()) {
  <div class="flex justify-content-between align-items-center border-bottom-1 pb-3 surface-border">
    <span class="font-medium text-lg lg:text-xl" [ngClass]="{ 'primary-purple-color': !editMode() }">
      <i class="pi pi-shopping-cart text-xl mr-2"></i>Your Order</span>
    @if (!inCheckout()) {
    <span class=""><i class="pi pi-times cursor-pointer h-2rem w-2rem flex align-items-center justify-content-center"
        (click)="this.generalService.cartSidebarVisible.set(false)"></i>
    </span>
    }
  </div>
  }
  <div class="offcanvas-body">
    <p-scrollPanel [style]="{ width: '100%', height: '100%' }" styleClass="customsidebar-scroll">
      <!-- @if (basket() !== null) {
  {{basket().basket?.basketItems | json}}
  } -->

      @defer(when !basket$().loading) {



      @if (basket$().loading && !basket$()) {

      <div class="h-full flex align-items-center justify-content-center">
        <p-progressSpinner ariaLabel="loading" />
      </div>


      }

      @if(editMode()) {

      @if (basket$() !== null) {

      <div class="grid grid-nogutter">
        @for (product of basket().basket?.basketItems; track product.id) {

        <app-checkout-sidebar-basket-item [basketItem]="product"></app-checkout-sidebar-basket-item>

        }



      </div>

      }


      <div class="flex w-full align-items-center justify-content-center">
        <span class="w-full saving-bg 500 text-white font-semibold px-2 py-2 border-round text-center">
          You have saved a total of 80 € 🙂
        </span>
      </div>

      <div class="py-2 m-0 surface-border">
        <div class="flex flex-column sm:flex-row gap-1 justify-content-between align-items-center">
          <span class="font-medium">Discount
            Code
            <div class="text-xs">
              Add here any discount codes you may have.
            </div>
            <div class="text-xs">
              Subject to Terms & Conditions.
            </div>
          </span>
          <span class="text-900">

            @if (isPromoCodeInputVisible()) {
            <div class="p-inputgroup mb-3">
              <input type="text" [(ngModel)]="promoCode" pInputText placeholder="Promo code" class="w-full">
              <button type="button" pButton pRipple label="Apply" [disabled]="!promoCode"></button>
            </div>
            } @else {

            <p-button class="promo_button" label="Add Code" [rounded]="true" severity="help" size="small"
              (click)="togglePromoCodeInputVisibility()" />

            }
          </span>
        </div>
      </div>


      } @else {

      <!-- empty basket -->
      @if (basket$().data !== null) {

      @if (basket().basket.basketItems.length === 0) {
      <div class="surface-section text-center px2 py-8 md:px-6 lg:px-4">

        <div class="flex flex-column align-items-center justify-content-center">

          <span
            class="flex align-items-center justify-content-center bg-cyan-100 text-cyan-800 mr-3 border-circle w-3rem h-3rem">
            <i class="pi pi-shopping-cart text-2xl"></i></span>
          <div class="text-xl text-900 font-bold mt-2">No items in your basket.</div>
          <p class="text-600 text-sm line-height-3 mb-1">
            Select the items you want to buy and add them to your basket.
          </p>
          @if (isGoToSelectNewPackageButtonVisible()) {
          <p>
            <p-button (click)="goToBuyPackage()" [rounded]="true" [outlined]="true" size="small"
              class="button_addon border-round-3xl" label="Buy a Package" icon="pi pi-chevron-right"
              iconPos="right"></p-button>
          </p>
          }
        </div>
      </div>
      }
      <ul class="list-none p-0 m-0">
        @for (product of basket().basket.basketItems; track product) {
        <app-checkout-sidebar-basket-item [basketItem]="product" productViewType="mini"
          [showActions]="inSidebar() && showCheckoutButton()"
          [sidebarShowDeleteCartItemsButton]="sidebarShowDeleteCartItemsButton()"></app-checkout-sidebar-basket-item>
        }

      </ul>
      }

      }

      <!-- end editmode -->

      @if(showCheckoutButton() && !inSidebar()) {
      <div class="mt-4">
        <p-button type="submit" (click)="goToCheckout()" [rounded]="true" [size]="'large'"
          class="azure-bg border-round-3xl" styleClass=" border-round-3xl" styleClass="" label="Checkout"
          [disabled]="basket().basket?.basketItems.length === 0" icon="pi pi-chevron-right" iconPos="right"></p-button>
      </div>
      }

      }@placeholder {
      <div class="h-full flex align-items-center justify-content-center">
        <p-progressSpinner ariaLabel="loading" />
      </div>
      }
      <!-- ends defer -->
    </p-scrollPanel>


  </div>

  @if (basket().basket !== null) {
  <!-- Modern Order Summary -->
  <div class="modern-order-summary">
    <div class="summary-content">
      <!-- Subtotal -->
      <div class="summary-row">
        <span class="summary-label">Subtotal</span>
        <span class="summary-value">{{basket().basket?.totalPrice}} €</span>
      </div>

      <!-- Package Discount -->
      @if (basket().basket?.totalPackOfLessonsSavings > 0) {
      <div class="summary-row discount-row">
        <span class="summary-label">
          <i class="pi pi-tag"></i>
          Package Discount
        </span>
        <span class="summary-value discount-value">-{{basket().basket?.totalPackOfLessonsSavings}} €</span>
      </div>
      }

      <!-- Group Discount -->
      @if (basket().basket?.totalGroupSavings > 0) {
      <div class="summary-row discount-row">
        <span class="summary-label">
          <i class="pi pi-users"></i>
          Group Discount
        </span>
        <span class="summary-value discount-value">-{{basket().basket?.totalGroupSavings}} €</span>
      </div>
      }

      <!-- Total -->
      <div class="summary-total">
        <span class="total-label">Total</span>
        <span class="total-value">{{basket().basket?.totalPrice}} €</span>
      </div>
    </div>
  </div>

  <!-- Modern Action Buttons -->
  @if (inSidebar() && showCheckoutButton() && basket().basket?.basketItems) {
  <div class="modern-checkout-actions">

    

    <p-button label=""  (click)="testMockCheckout()" [disabled]="basket().basket?.basketItems.length === 0" 
    styleClass="modern-btn secondary-btn fly w-full"
      [rounded]="true" iconPos="right">
        <span class="btn-text">Review Cart</span>
        <i class="pi pi-eye"></i>
    </p-button>



    <p-button label=""  (click)="testMockCheckout()" [disabled]="basket().basket?.basketItems.length === 0" styleClass="modern-btn primary-btn fly w-full"
      [rounded]="true" iconPos="right">
        <span class="btn-text">Checkout</span>
        <i class="pi pi-arrow-right"></i>
    </p-button>
  </div>
  }

  @if (showStepTwoButtonBottom()) {
  <div class="modern-payment-action flex justify-content-center mt-4">
    <button type="button" class="modern-btn payment-btn" (click)="goToCheckoutStep(2)">
      <span class="btn-content">
        @if (!generalService.miniLayoutSidebar()) {
        <span class="btn-text">Proceed to Payment</span>
        }
        <i class="pi pi-credit-card"></i>
      </span>
    </button>
  </div>
  }

  }

</div>

<p-confirmdialog #cd>
  <ng-template #headless let-message let-onAccept="onAccept" let-onReject="onReject">
    <div
      class="flex flex-column align-items-center justify-content-center p-3 bg-surface-0 dark:bg-surface-900 rounded">
      <!-- <img src="/assets/images/graphic/record_icon.svg" class="w-4rem mb-3" alt="Image" /> -->
      <span class="font-bold text-lg block mt-0 mb-2">{{ message.header }}</span>
      <p class="m-0 text-base" [innerHTML]="message.message"></p>
      <div class="flex items-center gap-2 mt-6">
        <p-button label="Yes, Remove" (onClick)="onAccept()" styleClass="w-32"></p-button>
        <p-button label="Not Now" [outlined]="true" (onClick)="onReject()" styleClass="w-32"></p-button>
      </div>
    </div>
  </ng-template>
</p-confirmdialog>