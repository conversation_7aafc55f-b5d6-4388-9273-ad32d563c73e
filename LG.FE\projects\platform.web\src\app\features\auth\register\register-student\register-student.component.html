<div class="card height-100 w-full surface-ground  text-600 overflow-hidden">

    <div class="grid p-2 mt-5">
        <div class="md:col-3 lg:col-2">

            <div class="steps-indicator">
                <div class="step-circle" *ngFor="let step of totalStepsArray"
                    [ngClass]="{ 'active': currentStep === step }" (click)="goToStep(step)">
                </div>
            </div>
        </div>
        <div class="md:col-9 lg:col-5">

            @defer (on viewport; when currentStep === 1) {
             
                <h2 class="text-xl m-0">Register a Student/s</h2>
                <p>Create a Student Profile for your children by filling out the form below.</p>
                <p>
                    Set up his/her sign in details and start with a free lesson.
                </p>

                <h3>How many students are you registering?</h3>

                <p-dropdown (onChange)="onStudentsNumChange($event)"
                    [styleClass]="'w-10rem simple-form-input dropdown-ct '" [options]="studentsNum"
                    [(ngModel)]="selectedStudentsNum" [virtualScroll]="true" [virtualScrollItemSize]="38"
                    placeholder="1-10"></p-dropdown>
              } @placeholder {
                <span>loading</span>
              }

            <div class="flex gap-1 w-full mt-4">

                <p-button class="mr-2" (click)="goToPreviousStep()" *ngIf="currentStep > 1" label="Back"
                    icon="pi pi-chevron-left" iconPos="left" styleClass="btn-standard btn-outline-secondary"></p-button>

                <p-button class="flex-1" (click)="goToNextStep()" styleClass="btn-standard btn-secondary" label="Save & Next"
                    icon="pi pi-chevron-right" iconPos="right"></p-button>
            </div>
        </div>
    </div>



    <div class=" overflow-hidden" #bottomImage>
        <div class="graphic-image ">
            @defer (on viewport(bottomImage)) {
            <img src="assets/images/auth/graphic-scene.webp" alt="Image">
            } @placeholder {

            }
        </div>
    </div>
</div>