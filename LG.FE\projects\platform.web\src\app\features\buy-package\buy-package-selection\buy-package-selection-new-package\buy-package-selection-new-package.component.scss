@use "mixins";

:host {
  display: block;

  // Modern Package Wizard Variables
  --wizard-primary: var(--deep-lavender-dark);
  --wizard-secondary: var(--deep-lavender);
  --wizard-success: #10b981;
  --wizard-warning: #f59e0b;
  --wizard-accent: #ec4899;
  --wizard-info: #06b6d4;
  --wizard-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --wizard-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --wizard-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
  --wizard-glow: 0 0 20px rgba(99, 102, 241, 0.15);
}

// Modern Package Wizard
.modern-package-wizard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
  border-radius: 16px;
  box-shadow: var(--wizard-glow), var(--wizard-shadow);
  position: relative;
  overflow: hidden;

  @include mixins.breakpoint(mobile) {
    padding: 1rem;
    margin: 0.5rem;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 50%, rgba(16, 185, 129, 0.02) 100%);
    pointer-events: none;
  }
}

// Wizard Content
.wizard-content {
  position: relative;
  z-index: 2;
}

// Loading State
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;

  i {
    font-size: 2rem;
    color: var(--wizard-primary);
  }

  span {
    font-size: 1rem;
    color: #6b7280;
    font-weight: 500;
  }
}

// Empty State Card
.empty-state-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--wizard-shadow);
}

// Progress Indicator
.wizard-progress {
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;

  @include mixins.breakpoint(mobile) {
    margin-bottom: 1.5rem;
  }

  .progress-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    @include mixins.breakpoint(mobile) {
      gap: 0.5rem;
    }

    .progress-step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      transition: var(--wizard-transition);

      @include mixins.breakpoint(mobile) {
        gap: 0.375rem;
      }

      &.active {
        .step-circle {
          background: linear-gradient(135deg, var(--wizard-primary) 0%, var(--wizard-secondary) 100%);
          color: white;
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .step-label {
          color: var(--wizard-primary);
          font-weight: 600;
        }
      }

      &.completed {
        .step-circle {
          background: linear-gradient(135deg, var(--wizard-success) 0%, #059669 100%);
          color: white;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .step-label {
          color: var(--wizard-success);
          font-weight: 600;
        }
      }

      .step-circle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 3rem;
        height: 3rem;
        background: rgba(226, 232, 240, 0.8);
        color: #6b7280;
        border-radius: 50%;
        font-size: 1.125rem;
        transition: var(--wizard-transition);

        @include mixins.breakpoint(mobile) {
          width: 2.5rem;
          height: 2.5rem;
          font-size: 1rem;
        }
      }

      .step-label {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;
        transition: var(--wizard-transition);

        @include mixins.breakpoint(mobile) {
          font-size: 0.75rem;
        }
      }
    }

    .progress-line {
      flex: 1;
      height: 2px;
      background: rgba(226, 232, 240, 0.6);
      border-radius: 1px;
      transition: var(--wizard-transition);
      max-width: 100px;

      @include mixins.breakpoint(mobile) {
        max-width: 50px;
      }

      &.completed {
        background: linear-gradient(90deg, var(--wizard-success) 0%, #059669 100%);
      }
    }
  }
}

// Two-Column Setup Section
.two-column-setup-section {
  margin-bottom: 2rem;

  @include mixins.breakpoint(mobile) {
    margin-bottom: 1.5rem;
  }

  .setup-header {
    text-align: center;
    margin-bottom: 2rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.5rem;
    }

    .section-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      font-size: 1.375rem;
      font-weight: 800;
      background: linear-gradient(135deg, var(--wizard-primary) 0%, var(--wizard-secondary) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0 0 0.75rem 0;
      line-height: 1.3;

      @include mixins.breakpoint(mobile) {
        font-size: 1.5rem;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
      }

      i {
        background: linear-gradient(135deg, var(--wizard-warning) 0%, var(--wizard-accent) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.5rem;
        // animation: sparkle 2s ease-in-out infinite;

        @include mixins.breakpoint(mobile) {
          font-size: 1.25rem;
        }
      }
    }

    .section-subtitle {
      font-size: 1rem;
      color: #6b7280;
      margin: 0;
      font-weight: 500;

      @include mixins.breakpoint(mobile) {
        font-size: 0.9rem;
      }
    }
  }

  .two-column-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;

    @include mixins.breakpoint(tablet) {
      grid-template-columns: 1fr 1.2fr; // Left column slightly smaller
      align-items: stretch; // Equal height cards
    }

    @include mixins.breakpoint(mobile) {
      gap: 1rem;
    }
  }

  .left-column,
  .right-column {
    display: flex;
    flex-direction: column;

    @include mixins.breakpoint(tablet) {

      // Ensure cards stretch to full height
      .setup-card {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
    }
  }
}

// Setup Cards
.setup-card {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: 16px;
  padding: 1.5rem;
  transition: var(--wizard-transition);
  position: relative;
  overflow: hidden;

  @include mixins.breakpoint(mobile) {
    padding: 1.25rem;
  }


  &:hover {
    box-shadow: var(--wizard-shadow-hover);
    border-color: rgba(99, 102, 241, 0.3);

    &::before {
      opacity: 1;
    }
  }

  &.learning-style-card::before {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.03) 100%);
  }

  &.unified-selection-card::before {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(34, 197, 94, 0.03) 100%);
  }

  &.placeholder-card::before {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.05) 0%, rgba(209, 213, 219, 0.03) 100%);
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    position: relative;
    z-index: 2;
    margin-bottom: 1rem;

    @include mixins.breakpoint(mobile) {
      font-size: 1rem;
      margin-bottom: 0.875rem;
      gap: 0.375rem;
    }

    i {
      font-size: 1rem;
      color: var(--wizard-primary);

      @include mixins.breakpoint(mobile) {
        font-size: 0.9rem;
      }
    }
  }


  .create-group-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1rem;
    background: linear-gradient(135deg, var(--wizard-success) 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--wizard-transition);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);

    @include mixins.breakpoint(mobile) {
      padding: 0.75rem 1.125rem;
      font-size: 0.85rem;
      justify-content: center;
      gap: 0.625rem;
    }

    &:hover {
      // box-shadow: 0 6px 20px rgba(16, 185, 129, 0.35);

      // .btn-glow {
      //   opacity: 1;
      // }
    }

    &:active {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .btn-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5rem;
      height: 1.5rem;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      transition: var(--wizard-transition);
      flex-shrink: 0;

      @include mixins.breakpoint(mobile) {
        width: 1.625rem;
        height: 1.625rem;
      }

      i {
        font-size: 0.75rem;
        font-weight: bold;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }
    }

    .btn-text {
      font-weight: 600;
      letter-spacing: 0.025em;
      position: relative;
      z-index: 2;
    }

    .btn-glow {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
      border-radius: 10px;
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
      transition: var(--wizard-transition);
      pointer-events: none;
    }

    // Subtle pulse animation
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
      border-radius: 10px;
      opacity: 0;
      animation: subtle-pulse 3s ease-in-out infinite;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;

    @include mixins.breakpoint(mobile) {
      flex-direction: column;
      gap: 0.75rem;
      align-items: stretch;
      margin-bottom: 0.875rem;
    }

  }

  .selection-content {
    position: relative;
    z-index: 2;
  }
}

// Unified Selection Card
.unified-selection-card {
  @include mixins.breakpoint(tablet) {

    // Ensure content fills the card height properly
    .selection-section {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .language-section {
      margin-top: auto; // Push language section to bottom when card stretches
    }
  }

  .selection-section {
    // margin-bottom: 1.5rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.25rem;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      position: relative;
      z-index: 2;

      @include mixins.breakpoint(mobile) {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
        margin-bottom: 0.875rem;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.125rem;
        font-weight: 700;
        color: #1f2937;
        margin: 0;

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
          gap: 0.375rem;
        }

        i {
          font-size: 1rem;
          color: var(--wizard-primary);

          @include mixins.breakpoint(mobile) {
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  .language-section {
    .section-divider {
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(226, 232, 240, 0.8) 20%, rgba(226, 232, 240, 0.8) 80%, transparent 100%);
      margin: 1.5rem 0;

      @include mixins.breakpoint(mobile) {
        margin: 1.25rem 0;
      }
    }

    .section-header {
      margin-bottom: 1rem;

      @include mixins.breakpoint(mobile) {
        margin-bottom: 0.875rem;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.125rem;
        font-weight: 700;
        color: #1f2937;
        margin: 0;
        justify-content: center;

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
          gap: 0.375rem;
        }

        i {
          font-size: 1rem;
          color: var(--wizard-info);

          @include mixins.breakpoint(mobile) {
            font-size: 0.9rem;
          }
        }
      }
    }
  }
}

// Placeholder Card
.placeholder-card {
  @include mixins.breakpoint(tablet) {

    // Ensure placeholder content fills the equal height card
    .placeholder-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center; // Center content vertically in equal height card
    }
  }

  .placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
    min-height: 200px;

    @include mixins.breakpoint(mobile) {
      padding: 1.5rem;
      min-height: 150px;
    }

    i {
      font-size: 2rem;
      color: #9ca3af;
      margin-bottom: 1rem;
      animation: pulse 2s ease-in-out infinite;

      @include mixins.breakpoint(mobile) {
        font-size: 1.75rem;
        margin-bottom: 0.875rem;
      }
    }

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #6b7280;
      margin: 0 0 0.5rem 0;

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
        margin-bottom: 0.375rem;
      }
    }

    p {
      font-size: 0.875rem;
      color: #9ca3af;
      margin: 0;
      line-height: 1.4;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8rem;
      }
    }
  }
}

// Learning Style Card
.learning-style-card {
  @include mixins.breakpoint(tablet) {

    // Ensure content is properly distributed in equal height card
    .learning-type-options {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center; // Center the options vertically
    }
  }
}

// Learning Type Options
.learning-type-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  @include mixins.breakpoint(mobile) {
    gap: 0.625rem;
  }

  .type-option {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    padding: 0.875rem;
    background: rgba(248, 250, 252, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--wizard-transition);
    position: relative;
    overflow: hidden;

    @include mixins.breakpoint(mobile) {
      gap: 0.75rem;
      padding: 0.75rem;
    }

    &:hover {
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
      border-color: rgba(99, 102, 241, 0.3);

      &::before {
        opacity: 1;
      }

      .option-icon {
      }
    }

    &.selected {
      border-color: var(--wizard-primary);
      background: rgba(99, 102, 241, 0.08);

      &::before {
        opacity: 1;
      }

      .option-icon {
        background: linear-gradient(135deg, var(--wizard-primary) 0%, var(--wizard-secondary) 100%);
        color: white;
        transform: scale(1.05);
      }

      .option-check {
        opacity: 1;
        transform: scale(1);
      }
    }

    .option-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.5rem;
      height: 2.5rem;
      background: rgba(226, 232, 240, 0.8);
      color: #6b7280;
      border-radius: 10px;
      transition: var(--wizard-transition);
      position: relative;
      z-index: 2;

      @include mixins.breakpoint(mobile) {
        width: 2.25rem;
        height: 2.25rem;
      }

      i {
        font-size: 1.125rem;

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
        }
      }
    }

    .option-content {
      flex: 1;
      position: relative;
      z-index: 2;

      h4 {
        font-size: 1rem;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 0.25rem 0;
        line-height: 1.3;

        @include mixins.breakpoint(mobile) {
          font-size: 0.9rem;
        }
      }

      p {
        font-size: 0.8rem;
        color: #6b7280;
        margin: 0;
        line-height: 1.4;
        font-weight: 500;

        @include mixins.breakpoint(mobile) {
          font-size: 0.75rem;
        }
      }
    }

    .option-check {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5rem;
      height: 1.5rem;
      background: linear-gradient(135deg, var(--wizard-success) 0%, #059669 100%);
      color: white;
      border-radius: 50%;
      font-size: 0.75rem;
      opacity: 0;
      transform: scale(0.8);
      transition: var(--wizard-transition);
      position: relative;
      z-index: 2;

      @include mixins.breakpoint(mobile) {
        width: 1.375rem;
        height: 1.375rem;
        font-size: 0.7rem;
      }
    }
  }
}

// Language Selector
.language-selector {
  .input-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    text-align: center;

    @include mixins.breakpoint(mobile) {
      font-size: 0.8rem;
      margin-bottom: 0.625rem;
    }
  }
}

// Status Messages
.status-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 12px;
  margin-top: 1.5rem;
  font-weight: 500;

  @include mixins.breakpoint(mobile) {
    gap: 0.5rem;
    padding: 0.875rem;
    margin-top: 1.25rem;
  }

  i {
    font-size: 1.125rem;
    flex-shrink: 0;

    @include mixins.breakpoint(mobile) {
      font-size: 1rem;
    }
  }

  span {
    font-size: 0.875rem;
    line-height: 1.4;

    @include mixins.breakpoint(mobile) {
      font-size: 0.8rem;
    }
  }

  &.info {
    background: rgba(6, 182, 212, 0.1);
    border: 1px solid rgba(6, 182, 212, 0.2);
    color: #0891b2;

    i {
      color: var(--wizard-info);
    }
  }

  &.warning {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    color: #d97706;

    i {
      color: var(--wizard-warning);
    }
  }
}

// Empty Groups State - Compact & Modern
.empty-groups-state {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 1rem 1.25rem;
  background: rgba(248, 250, 252, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.4);
  border-radius: 10px;
  transition: var(--wizard-transition);

  @include mixins.breakpoint(mobile) {
    gap: 0.75rem;
    padding: 0.875rem 1rem;
  }

  &:hover {
    background: rgba(248, 250, 252, 0.8);
    border-color: rgba(226, 232, 240, 0.6);
  }

  .empty-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.25rem;
    height: 2.25rem;
    background: rgba(156, 163, 175, 0.1);
    border-radius: 8px;
    flex-shrink: 0;

    @include mixins.breakpoint(mobile) {
      width: 2rem;
      height: 2rem;
    }

    i {
      font-size: 1rem;
      color: #9ca3af;

      @include mixins.breakpoint(mobile) {
        font-size: 0.9rem;
      }
    }
  }

  .empty-content {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    flex: 1;

    .empty-title {
      font-size: 0.875rem;
      font-weight: 600;
      color: #6b7280;
      line-height: 1.2;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8rem;
      }
    }

    .empty-subtitle {
      font-size: 0.75rem;
      color: #9ca3af;
      line-height: 1.3;
      font-weight: 500;

      @include mixins.breakpoint(mobile) {
        font-size: 0.7rem;
      }
    }
  }
}

// Unified Package Selection Section
.unified-package-section {
  margin-top: 2rem;

  @include mixins.breakpoint(mobile) {
    margin-top: 1.5rem;
  }

  .package-header {
    text-align: center;
    margin-bottom: 2rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.5rem;
    }

    .section-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      font-size: 1.475rem;
      font-weight: 800;
      background: linear-gradient(135deg, var(--wizard-success) 0%, #059669 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0 0 0.75rem 0;
      line-height: 1.3;

      @include mixins.breakpoint(mobile) {
        font-size: 1.5rem;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
      }

      i {
        background: linear-gradient(135deg, var(--wizard-success) 0%, #059669 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.5rem;

        @include mixins.breakpoint(mobile) {
          font-size: 1.25rem;
        }
      }
    }

    .section-subtitle {
      font-size: 1rem;
      color: #6b7280;
      margin: 0;
      font-weight: 500;

      @include mixins.breakpoint(mobile) {
        font-size: 0.9rem;
      }
    }
  }
}

// Aligned Selection Container
.aligned-selection-container {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 16px;
  padding: 1rem;
  backdrop-filter: blur(10px);

  @include mixins.breakpoint(mobile) {
    padding: 1.5rem;
  }

  .duration-selection,
  .packages-selection {
    margin-bottom: 2rem;

    &:last-child {
      margin-bottom: 0;
    }

    @include mixins.breakpoint(mobile) {
      margin-bottom: 1.5rem;
    }

    .subsection-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 1.25rem;
      font-weight: 700;
      color: #1f2937;
      margin: 0 0 1.5rem 0;
      justify-content: center;

      @include mixins.breakpoint(mobile) {
        font-size: 1.125rem;
        margin-bottom: 1.25rem;
        gap: 0.375rem;
      }

      i {
        font-size: 1rem;
        color: var(--wizard-primary);

        @include mixins.breakpoint(mobile) {
          font-size: 0.9rem;
        }
      }
    }
  }
}

// PrimeFlex Grid Duration Layout
.duration-selection {
  .grid {
    margin: 0;

    // Add consistent spacing between grid items
    .col-12 {
      padding: 0.75rem;

      @include mixins.breakpoint(mobile) {
        padding: 0.5rem;
      }
    }
  }

  .duration-item-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    // Ensure duration boxes fill the available height
    app-buy-package-duration-box {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}

// PrimeFlex Grid Package Layout
.packages-selection {
  .grid {
    margin: 0;

    // Add consistent spacing between grid items
    // .col-12 {
    //   padding: 0.75rem;

    //   @include mixins.breakpoint(mobile) {
    //     padding: 0.5rem;
    //   }
    // }
  }

  .package-item-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    // Ensure pricing boxes fill the available height
    app-buy-package-pricing-box {
      height: 100%;
      display: flex;
      flex-direction: column;

      .modern-pricing-card {
        height: 100%;
        display: flex;
        flex-direction: column;

        .card-content {
          flex: 1;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
}

// Language Selection Prompt
.language-selection-prompt {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  width: 100%;

  @include mixins.breakpoint(mobile) {
    margin-top: 1.5rem;
  }

  .prompt-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem 1.75rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
    border: 2px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    transition: var(--wizard-transition);
    max-width: 500px;

    @include mixins.breakpoint(mobile) {
      padding: 1rem 1.25rem;
      gap: 0.875rem;
      flex-direction: column;
      text-align: center;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 0 25px rgba(16, 185, 129, 0.2), 0 8px 25px rgba(0, 0, 0, 0.15);
      border-color: rgba(16, 185, 129, 0.4);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.03) 0%, rgba(34, 197, 94, 0.02) 100%);
      pointer-events: none;
    }

    .prompt-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 3rem;
      height: 3rem;
      background: linear-gradient(135deg, var(--wizard-success) 0%, #059669 100%);
      color: white;
      border-radius: 12px;
      flex-shrink: 0;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      position: relative;
      z-index: 2;

      @include mixins.breakpoint(mobile) {
        width: 2.75rem;
        height: 2.75rem;
      }

      i {
        font-size: 1.25rem;
        animation: gentle-pulse 2s ease-in-out infinite;

        @include mixins.breakpoint(mobile) {
          font-size: 1.125rem;
        }
      }
    }

    .prompt-content {
      flex: 1;
      position: relative;
      z-index: 2;

      .prompt-title {
        font-size: 1.125rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--wizard-success) 0%, #059669 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0 0 0.375rem 0;
        line-height: 1.3;

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
          margin-bottom: 0.25rem;
        }
      }

      .prompt-text {
        font-size: 0.875rem;
        color: #6b7280;
        margin: 0;
        line-height: 1.4;
        font-weight: 500;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8rem;
        }
      }
    }

    .prompt-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.25rem;
      height: 2.25rem;
      background: rgba(16, 185, 129, 0.1);
      color: var(--wizard-success);
      border-radius: 50%;
      flex-shrink: 0;
      position: relative;
      z-index: 2;

      @include mixins.breakpoint(mobile) {
        width: 2rem;
        height: 2rem;
        order: -1;
      }

      i {
        font-size: 1rem;
        animation: bounce-arrow 2s ease-in-out infinite;

        @include mixins.breakpoint(mobile) {
          font-size: 0.9rem;
          transform: rotate(180deg); // Point down on mobile
        }
      }
    }
  }
}

// Animations
@keyframes sparkle {

  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }

  25% {
    transform: scale(1.1) rotate(90deg);
    opacity: 0.8;
  }

  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }

  75% {
    transform: scale(1.1) rotate(270deg);
    opacity: 0.8;
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes subtle-pulse {

  0%,
  100% {
    opacity: 0;
  }

  50% {
    opacity: 0.3;
  }
}

@keyframes gentle-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes bounce-arrow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}