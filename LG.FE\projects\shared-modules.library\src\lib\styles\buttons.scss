@use "variables" as v;
@use "mixins";

// ========================================
// STANDARDIZED BUTTON SYSTEM
// ========================================
//
// This file contains a comprehensive button system that follows
// modern UI/UX principles: compact, minimal, professional design
// with consistent color usage from CSS variables.
//
// USAGE GUIDE:
//
// 1. BASE CLASS: Always use .btn-standard as the foundation
//    <button class="btn-standard btn-primary">Primary Button</button>
//
// 2. VARIANTS: Choose from semantic color variants
//    - .btn-primary (main actions)
//    - .btn-secondary (alternative actions)
//    - .btn-success (positive actions)
//    - .btn-warning (caution actions)
//    - .btn-danger (destructive actions)
//    - .btn-info (informational actions)
//
// 3. STYLES: Choose button style
//    - Solid: .btn-primary (default)
//    - Outlined: .btn-outline-primary
//    - Soft: .btn-soft-primary-new
//    - Ghost: .btn-ghost
//    - Link: .btn-link
//
// 4. SIZES: Optional size modifiers
//    - .btn-sm (small)
//    - Default (medium)
//    - .btn-lg (large)
//    - .btn-block (full width)
//
// 5. SPECIAL TYPES:
//    - .btn-cta (call-to-action with gradient)
//
// EXAMPLES:
// <button class="btn-standard btn-primary btn-lg">Large Primary</button>
// <button class="btn-standard btn-outline-secondary btn-sm">Small Outlined</button>
// <button class="btn-standard btn-soft-primary-new btn-block">Full Width Soft</button>
//
// ========================================

.cta-btn .p-button {
  position: relative;
  display: block;
  background: white;
  border: 0;
  width: 100%;
  padding-top: 5px;
  padding-bottom: 5px;
  margin-bottom: 20px;
  font-size: 18px;
  border-radius: var(--btn-border-radius-sm);
  text-decoration: none;
  transition: var(--btn-transition);
  border: 1px solid var(--btn-primary);
  color: var(--btn-primary);

  &:hover {
    background: var(--btn-primary);
    color: white;

    strong {
      color: white !important;
    }
  }

  strong {
    color: var(--btn-primary);
  }

  &:hover strong {
    color: white;
  }

  i {
    position: absolute;
    right: 30px;
    top: 25px;
    height: 15px;
  }
}


.btn-soft-primary {
  background-color: rgba(var(--btn-primary-rgb), 0.1);
  border-color: transparent;
  color: var(--btn-primary);
  transition: var(--btn-transition);
  border-radius: var(--btn-border-radius);
}

.btn-soft-primary:active,
.btn-soft-primary:focus,
.btn-soft-primary:hover {
  background-color: rgba(var(--btn-primary-rgb), 0.15);
  border-color: transparent;
  color: var(--btn-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--btn-shadow-hover);
}

.btn-soft-secondary {
  background-color: #74788d1a;
  border-color: #0000;
  color: #74788d;
  transition: all .2s ease
}

.btn-soft-secondary:active,
.btn-soft-secondary:focus,
.btn-soft-secondary:hover {
  background-color: #74788d;
  border-color: #0000;
  color: #fff
}

.btn-soft-success {
  background-color: rgba(10, 133, 92, 0.1); // Using checkout-success color
  border-color: transparent;
  color: var(--btn-success);
  transition: var(--btn-transition);
  border-radius: var(--btn-border-radius);
}

.btn-soft-success:active,
.btn-soft-success:focus,
.btn-soft-success:hover {
  background-color: rgba(10, 133, 92, 0.15);
  border-color: transparent;
  color: var(--btn-success-hover);
  transform: translateY(-1px);
  box-shadow: var(--btn-shadow-hover);
}

.btn-soft-info {
  background-color: #50a5f11a;
  border-color: #0000;
  color: #0F6CBD;
  transition: all .2s ease
}

.btn-soft-info:active,
.btn-soft-info:focus,
.btn-soft-info:hover {
  background-color: #50a5f1;
  border-color: #0000;
  color: #fff
}

.btn-soft-warning {
  background-color: #f1b44c1a;
  border-color: #0000;
  color: #f1b44c;
  transition: all .2s ease
}

.btn-soft-warning:active,
.btn-soft-warning:focus,
.btn-soft-warning:hover {
  background-color: #f1b44c;
  border-color: #0000;
  color: #fff
}

.btn-soft-danger {
  background-color: #f46a6a1a;
  border-color: #0000;
  color: #f46a6a;
  transition: all .2s ease
}

.btn-soft-danger:active,
.btn-soft-danger:focus,
.btn-soft-danger:hover {
  background-color: #f46a6a;
  border-color: #0000;
  color: #fff
}

.btn-soft-pink {
  background-color: #e83e8c1a;
  border-color: #0000;
  color: #e83e8c;
  transition: all .2s ease
}

.btn-soft-pink:active,
.btn-soft-pink:focus,
.btn-soft-pink:hover {
  background-color: #e83e8c;
  border-color: #0000;
  color: #fff
}

.btn-soft-light {
  background-color: #eff2f71a;
  border-color: #0000;
  color: #eff2f7;
  transition: all .2s ease
}

.btn-soft-light:active,
.btn-soft-light:focus,
.btn-soft-light:hover {
  background-color: #eff2f7;
  border-color: #0000;
  color: #fff
}

.btn-soft-dark {
  background-color: #343a401a;
  border-color: #0000;
  color: #343a40;
  transition: all .2s ease
}

.btn-soft-dark:active,
.btn-soft-dark:focus,
.btn-soft-dark:hover {
  background-color: #343a40;
  border-color: #0000;
  color: #fff
}

// ========================================
// STANDARDIZED BUTTON SYSTEM
// Modern, compact, minimal, professional buttons
// Following established UI/UX preferences
// ========================================

// Base Button Class - Foundation for all buttons
.btn-standard {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: 2px solid transparent;
  border-radius: var(--btn-border-radius);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: var(--btn-transition);
  position: relative;
  overflow: hidden;
  box-shadow: var(--btn-shadow);

  // Responsive sizing
  @include mixins.breakpoint(mobile) {
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }

  // Disabled state
  &:disabled,
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;

    &:hover {
      transform: none !important;
      box-shadow: none !important;
    }
  }

  // Focus state for accessibility
  &:focus {
    outline: none;
    box-shadow: var(--btn-shadow-focus);
  }

  // Icon spacing
  i {
    font-size: 1em;
    transition: var(--btn-transition);
  }

  // Hover animation for icons
  &:hover:not(:disabled) i {
  }
}

// Button Sizes
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8125rem;
  border-radius: var(--btn-border-radius-sm);

  @include mixins.breakpoint(mobile) {
    padding: 0.4375rem 0.875rem;
    font-size: 0.75rem;
  }
}

.btn-lg {
  padding: 1rem 1.75rem;
  font-size: 1rem;
  border-radius: var(--btn-border-radius-lg);

  @include mixins.breakpoint(mobile) {
    padding: 0.875rem 1.5rem;
    font-size: 0.9375rem;
  }
}

// Full width button
.btn-block {
  width: 100%;
  justify-content: center;
}

// ========================================
// BUTTON VARIANTS - Semantic Colors
// ========================================

// Primary Button - Main actions
.btn-primary {
  background-color: var(--btn-primary);
  border-color: var(--btn-primary);
  color: white;

  &:hover:not(:disabled) {
    background-color: var(--btn-primary-hover);
    border-color: var(--btn-primary-hover);
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Secondary Button - Alternative actions
.btn-secondary {
  background-color: var(--btn-secondary);
  border-color: var(--btn-secondary);
  color: white;

  &:hover:not(:disabled) {
    background-color: var(--btn-secondary-hover);
    border-color: var(--btn-secondary-hover);
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Success Button - Positive actions
.btn-success {
  background-color: var(--btn-success);
  border-color: var(--btn-success);
  color: white;

  &:hover:not(:disabled) {
    background-color: var(--btn-success-hover);
    border-color: var(--btn-success-hover);
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Warning Button - Caution actions
.btn-warning {
  background-color: var(--btn-warning);
  border-color: var(--btn-warning);
  color: white;

  &:hover:not(:disabled) {
    background-color: var(--btn-warning-hover);
    border-color: var(--btn-warning-hover);
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Danger Button - Destructive actions
.btn-danger {
  background-color: var(--btn-danger);
  border-color: var(--btn-danger);
  color: white;

  &:hover:not(:disabled) {
    background-color: var(--btn-danger-hover);
    border-color: var(--btn-danger-hover);
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Info Button - Informational actions
.btn-info {
  background-color: var(--btn-info);
  border-color: var(--btn-info);
  color: white;

  &:hover:not(:disabled) {
    background-color: var(--btn-info-hover);
    border-color: var(--btn-info-hover);
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// ========================================
// OUTLINED BUTTON VARIANTS
// Professional, minimal outlined buttons
// ========================================

// Outlined Primary
.btn-outline-primary {
  background-color: transparent;
  border-color: var(--btn-primary);
  color: var(--btn-primary);

  &:hover:not(:disabled) {
    background-color: var(--btn-primary);
    border-color: var(--btn-primary);
    color: white;
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Outlined Secondary
.btn-outline-secondary {
  background-color: transparent;
  border-color: var(--btn-secondary);
  color: var(--btn-secondary);

  &:hover:not(:disabled) {
    background-color: var(--btn-secondary);
    border-color: var(--btn-secondary);
    color: white;
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Outlined Success
.btn-outline-success {
  background-color: transparent;
  border-color: var(--btn-success);
  color: var(--btn-success);

  &:hover:not(:disabled) {
    background-color: var(--btn-success);
    border-color: var(--btn-success);
    color: white;
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Outlined Warning
.btn-outline-warning {
  background-color: transparent;
  border-color: var(--btn-warning);
  color: var(--btn-warning);

  &:hover:not(:disabled) {
    background-color: var(--btn-warning);
    border-color: var(--btn-warning);
    color: white;
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Outlined Danger
.btn-outline-danger {
  background-color: transparent;
  border-color: var(--btn-danger);
  color: var(--btn-danger);

  &:hover:not(:disabled) {
    background-color: var(--btn-danger);
    border-color: var(--btn-danger);
    color: white;
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Outlined Info
.btn-outline-info {
  background-color: transparent;
  border-color: var(--btn-info);
  color: var(--btn-info);

  &:hover:not(:disabled) {
    background-color: var(--btn-info);
    border-color: var(--btn-info);
    color: white;
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// ========================================
// SOFT/GHOST BUTTON VARIANTS
// Subtle, modern button styles
// ========================================

// Soft Primary - Updated to use CSS variables
.btn-soft-primary-new {
  background-color: rgba(var(--btn-primary-rgb), 0.1);
  border-color: transparent;
  color: var(--btn-primary);

  &:hover:not(:disabled) {
    background-color: rgba(var(--btn-primary-rgb), 0.15);
    color: var(--btn-primary-hover);
    box-shadow: var(--btn-shadow-hover);
  }

  &:active:not(:disabled) {
  }
}

// Ghost Button - Minimal styling
.btn-ghost {
  background-color: transparent;
  border-color: transparent;
  color: var(--btn-primary);
  box-shadow: none;

  &:hover:not(:disabled) {
    background-color: rgba(var(--btn-primary-rgb), 0.05);
    color: var(--btn-primary-hover);
  }

  &:active:not(:disabled) {
  }
}

// ========================================
// SPECIAL BUTTON TYPES
// Context-specific button styles
// ========================================

// CTA Button - Call to Action
.btn-cta {
  background: linear-gradient(135deg, var(--btn-primary) 0%, var(--btn-primary-hover) 100%);
  border-color: var(--btn-primary);
  color: white;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--btn-primary-hover) 0%, var(--btn-primary) 100%);
    box-shadow: 0 6px 20px rgba(var(--btn-primary-rgb), 0.3);
  }

  &:active:not(:disabled) {
  }
}

// Link Button - Text-only button
.btn-link {
  background-color: transparent;
  border-color: transparent;
  color: var(--btn-primary);
  text-decoration: underline;
  text-underline-offset: 2px;
  box-shadow: none;
  padding: 0.25rem 0.5rem;

  &:hover:not(:disabled) {
    color: var(--btn-primary-hover);
    text-decoration: none;
  }

  &:active:not(:disabled) {
  }
}