<div class="">
    <div class="flex flex-column gap-4 align-items-center justify-content-center h-full overflow-hidden relative z-3
        ">

        <!-- {{this.editGroupState()}} -->
        @defer (on immediate) {

        <div class="w-full flex flex-column align-items-center justify-content-center z-3">

            <div class=" sm:px-3 w-full" style="border-radius:53px">

                @if (this.editGroupState() === GroupDialogState.CreateGroupSuggestionStep) {
                    
                <app-student-group-selection-suggestion-text-step
                    [isDialogPopup]="isDialogPopup()"
                    (buttonClicked)="changeGroupState(GroupDialogState.CreateGroup)"></app-student-group-selection-suggestion-text-step>
                } @else if (this.editGroupState() === GroupDialogState.CreateGroup || this.editGroupState() ===
                GroupDialogState.EditGroup) {

                @if (showTopTitle()) {

                <div class="text-center mb-3 m-auto md:max-w-30rem">
                    <div class="primary-purple-color text-3xl font-semibold mb-1">
                        {{title()}}
                    </div>
                    <span class="primary-purple-color font-medium text-sm sm:text-base">
                        {{paragraphText()}}
                    </span>
                </div>
                }

                @if (!isEditMode()) {
                <form [formGroup]="groupForm" (ngSubmit)="onSubmit()">


                    <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        Group Name
                    </h3>
                    <app-prime-reactive-form-input [showLabelAbove]="true" formControlName="groupName"
                        [parentForm]="groupForm" placeholder="Enter group name" [required]="false" data-name="groupName"
                        pAutoFocus [inputClass]="'w-full'" />

                    <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        Choose Language<span class="text-red-600">*</span>
                    </h3>
                    @if (teachingLanguages$() && teachingLanguages$().teachingLanguages!== null) {
                    <p-select (onChange)="onLanguageChange($event)" formControlName="teachingLanguageId" dataKey="id"
                        optionValue="id" [options]="teachingLanguages$().teachingLanguages" appendTo="body"
                        optionLabel="name" placeholder="Select language" styleClass="w-full">
                    </p-select>
                    <app-form-field-validation-message messageClass="text-red-500 mb-0"
                        [control]="groupForm.controls['teachingLanguageId']!" propertyName="language" />
                    }


                    <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt- primary-purple-color flex">
                        Select Students <span class="text-red-600">*</span>
                    </h3>
                    <p class="text-600 text-sm mt-0 mb-0 line-height-3">
                        Choose students with compatible language levels. 
                    </p>

                    @if (groupForm.controls['teachingLanguageId']?.value) {
                    <app-smart-student-selector [students]="availableStudentsForLanguage$()" [selectedStudents]="selectedStudents()"
                        [selectedLanguageName]="getSelectedLanguageName()"
                        [computedGroupLanguageLevels]="computedGroupLanguageLevels()"
                        [computedGroupLevelsText]="computedGroupLevelsText()"
                        [loading]="services.dataState.parentStudents.state().loading" [isEditMode]="false"
                        (studentSelected)="onStudentSelected($event)">
                    </app-smart-student-selector>
                    } @else {
                    <div
                        class="mt-1 flex flex-column md:flex-row align-items-center justify-content-between
                                             gap-3 px-3 py-2 border-round-md border-1 mb-3 bg-cyan-50 border-cyan-300 backdrop-blur-sm shadow-1">
                        <div class="flex align-items-center gap-2">
                            <i class="pi pi-info-circle text-cyan-700 text-xl"></i>
                            <p class="text-cyan-700 text-sm m-0 line-height-3">
                                Please select a language first to see available students.
                            </p>
                        </div>
                    </div>
                    }




                    <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        Group Availability<span class="text-red-600">*</span>
                    </h3>
                    @if (availabilityData() && availabilityDataLoaded()) {


                    <div class="mt-2">
                        @if (!areAllCommonTimeSlotsEmptyBool()) {

                        <div class="mt-3 flex flex-column md:flex-row align-items-center justify-content-between gap-3 px-3 py-2 border-round-xl border-1 mb-3 bg-green-50/50 border-green-300 backdrop-blur-sm shadow-1"
                            *ngIf="!isPanelClosed">
                            <div class="flex align-items-center gap-2">
                                <i class="pi pi-check-circle text-green-700 text-xl"></i>
                                <p class="text-green-700 text-sm m-0 line-height-3">
                                    We found some common timeslots for your students. Please review them
                                    carefully.
                                </p>
                            </div>
                            <p-button label="OK" icon="pi pi-check" severity="success" size="small"
                                (click)="isPanelClosed = true">
                            </p-button>
                        </div>
                        }

                        <app-availability-picker-days [showTimezoneDisplay]="false"
                            [noteText]="'Please note that all times are in your account\'s timezone.'"
                            [availabilityData]="availabilityData()" (timeSlotsChange)="onTimeSlotsChange($event)"
                            (availabilityFormValid)="onAvailabilityDaysFormValid($event)">
                            @if (this.editGroupState() === GroupDialogState.EditGroup) {
                            <app-availability-timezone-selector
                                [userBasicInfo]="services.auth.getUserProfileInfo()!.basicProfileInfoDto!"
                                [availabilityTimezone]="availabilityTimezone"
                                (timezoneChanged)="onTimeZoneChanged($event)"></app-availability-timezone-selector>
                            }
                        </app-availability-picker-days>
                        <app-form-field-validation-message messageClass="text-red-500 mb-0"
                            [control]="groupForm.controls?.availabilityDto?.controls?.['weekDayTimeSlots']!"
                            propertyName="availability" />

                    </div>
                    } @else {

                    <div class="mt-1 flex flex-column md:flex-row align-items-center justify-content-between
                                         gap-3 px-3 py-3 border-round-lg border-1 mb-3"
                        [class.bg-blue-50]="selectedStudents().length === 0"
                        [class.border-blue-300]="selectedStudents().length === 0"
                        [class.bg-red-50]="selectedStudents().length === 1"
                        [class.border-red-300]="selectedStudents().length === 1">
                        <div class="flex align-items-center gap-3">
                            <i class="pi pi-users" [class.text-blue-700]="selectedStudents().length === 0"
                                [class.text-red-600]="selectedStudents().length === 1" class="text-2xl"></i>
                            <div>
                                @if (selectedStudents().length === 0) {
                                <p class="text-blue-700 text-sm font-medium m-0 line-height-3">
                                    Student Selection Required
                                </p>
                                <p class="text-blue-600 text-xs m-0 line-height-3 mt-1">
                                    To set up availability time slots, please choose your students first.
                                </p>
                                } @else if (selectedStudents().length === 1) {
                                <p class="text-red-600 text-sm font-medium m-0 line-height-3">
                                    More Students Required
                                </p>
                                <p class="text-red-500 text-xs m-0 line-height-3 mt-1">
                                    At least 2 students are required to create a group and set availability.
                                </p>
                                }
                            </div>
                        </div>
                    </div>
                    }

                    <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        Student Level <span class="text-red-600">*</span>
                    </h3>
                    <p-select formControlName="studentLevel" dataKey="code" optionValue="code" [options]="studentLevels"
                        appendTo="body" optionLabel="name" placeholder="Select student level" styleClass="w-full">
                    </p-select>
                    <app-form-field-validation-message messageClass="text-red-500 mb-0"
                        [control]="groupForm.controls['studentLevel']!" propertyName="student level" />

                    <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        More Details
                    </h3>
                    <textarea pTextarea [autoResize]="true" class="w-full h-9rem" cols="35"
                        formControlName="moreDetails"
                        placeholder="Tell us more about the students, their current level, schedule and goals they want to achieve by creating this group."
                        rows="5"></textarea>


                    @if (!isDialogPopup()) {
                    <div class="flex flex-column align-items-center justify-content-center">
                        <p-button type="submit" icon="pi pi-check-circle" iconPos="left" label="Create Group"
                            [loading]="services.apiLoadingStateService.getIsLoading()"
                            [styleClass]="canCreateGroup() ? 'bg-indigo-600 mt-2' : 'mt-2'"
                            [severity]="canCreateGroup() ? 'primary' : 'secondary'"></p-button>
                    </div>
                    }

                </form>



                <!-- Group Requirements Section -->
                <div class="group-requirements-section mb-4 mt-2">
                    <h3 class="align-items-center line-height-1 text-sm font-semibold mb-2 primary-purple-color flex">
                        <i class="pi pi-users mr-2"></i>
                        Group Requirements
                    </h3>

                    <div class="requirements-card p-3 border-round-lg border-1"
                        [class.border-green-300]="hasMinimumStudents() && groupForm.controls['teachingLanguageId']!.value && groupForm.controls['studentLevel']!.value"
                        [class.bg-green-50]="hasMinimumStudents() && groupForm.controls['teachingLanguageId']!.value && groupForm.controls['studentLevel']!.value"
                        [class.border-orange-300]="!hasMinimumStudents() || !groupForm.controls['teachingLanguageId']!.value || !groupForm.controls['studentLevel']!.value"
                        [class.bg-orange-50]="!hasMinimumStudents() || !groupForm.controls['teachingLanguageId']!.value || !groupForm.controls['studentLevel']!.value">

                        <div class="requirements-list">
                            <!-- Language Requirement -->
                            <div class="requirement-item flex align-items-center gap-2 mb-2">
                                <i class="pi" [class.pi-check-circle]="groupForm.controls['teachingLanguageId']!.value"
                                    [class.text-green-600]="groupForm.controls['teachingLanguageId']!.value"
                                    [class.pi-circle]="!groupForm.controls['teachingLanguageId']!.value"
                                    [class.text-orange-500]="!groupForm.controls['teachingLanguageId']!.value"></i>
                                <span class="text-sm"
                                    [class.text-green-700]="groupForm.controls['teachingLanguageId']!.value"
                                    [class.font-medium]="groupForm.controls['teachingLanguageId']!.value"
                                    [class.text-orange-700]="!groupForm.controls['teachingLanguageId']!.value">
                                    Teaching language selected
                                </span>
                                @if (groupForm.controls['teachingLanguageId']!.value) {
                                <span class="text-xs text-500 ml-auto">{{ getSelectedLanguageName() }}</span>
                                }
                            </div>

                            <!-- Student Level Requirement -->
                            <div class="requirement-item flex align-items-center gap-2 mb-2">
                                <i class="pi" [class.pi-check-circle]="groupForm.controls['studentLevel']!.value"
                                    [class.text-green-600]="groupForm.controls['studentLevel']!.value"
                                    [class.pi-circle]="!groupForm.controls['studentLevel']!.value"
                                    [class.text-orange-500]="!groupForm.controls['studentLevel']!.value"></i>
                                <span class="text-sm" [class.text-green-700]="groupForm.controls['studentLevel']!.value"
                                    [class.font-medium]="groupForm.controls['studentLevel']!.value"
                                    [class.text-orange-700]="!groupForm.controls['studentLevel']!.value">
                                    Student level selected
                                </span>
                                @if (groupForm.controls['studentLevel']!.value) {
                                <span class="text-xs text-500 ml-auto">{{ getStudentLevelName() }}</span>
                                }
                            </div>

                            <!-- Students Requirement -->
                            <div class="requirement-item flex align-items-center gap-2 mb-2">
                                <i class="pi" [class.pi-check-circle]="hasMinimumStudents()"
                                    [class.text-green-600]="hasMinimumStudents()"
                                    [class.pi-circle]="!hasMinimumStudents()"
                                    [class.text-orange-500]="!hasMinimumStudents()"></i>
                                <span class="text-sm" [class.text-green-700]="hasMinimumStudents()"
                                    [class.font-medium]="hasMinimumStudents()"
                                    [class.text-orange-700]="!hasMinimumStudents()">
                                    At least 2 students selected
                                </span>
                                <span class="text-xs ml-auto" [class.text-green-600]="hasMinimumStudents()"
                                    [class.text-orange-600]="!hasMinimumStudents()">
                                    {{ selectedStudents().length }}/2 minimum
                                </span>
                            </div>

                            <!-- Computed Group Language Level -->
                            @if (selectedStudents().length >= 2 && groupForm.controls['teachingLanguageId']!.value) {
                            <div class="requirement-item flex align-items-center gap-2 mb-2">
                                <i class="pi pi-info-circle text-blue-600"></i>
                                <span class="text-sm text-blue-700">
                                    Group language level
                                </span>
                                <span class="text-xs ml-auto">
                                    <span class="language-level-badges">
                                        @for (level of computedGroupLanguageLevels(); track level) {
                                        <span class="level-badge" [attr.data-level]="level">
                                            {{ getLanguageLevelDisplayName(level) }}
                                        </span>
                                        }
                                        @if (computedGroupLanguageLevels().length === 0) {
                                        <span class="level-badge na">N/A</span>
                                        }
                                    </span>
                                </span>
                            </div>
                            }

                            <!-- Overall Status -->
                            <div class="requirement-status mt-3 pt-2 border-top-1 border-200">
                                @if (canCreateGroup()) {
                                <div class="flex align-items-center gap-2">
                                    <i class="pi pi-check-circle text-green-600"></i>
                                    <span class="text-green-700 text-sm font-medium">Ready to create group!</span>
                                </div>
                                } @else {
                                <div class="flex align-items-center gap-2">
                                    <i class="pi pi-exclamation-triangle text-orange-500"></i>
                                    <span class="text-orange-700 text-sm">Complete all requirements above to
                                        proceed</span>
                                </div>

                                <!-- Debug: Form Validation Errors -->
                                <!-- @if (getFormValidationErrors().length > 0) {
                                                    <div class="form-debug mt-2 p-2 bg-red-50 border-red-200 border-1 border-round text-xs">
                                                        <div class="font-semibold text-red-700 mb-1">Form Validation Issues:</div>
                                                        @for (error of getFormValidationErrors(); track error) {
                                                            <div class="text-red-600">• {{ error }}</div>
                                                        }
                                                    </div>
                                                } -->
                                }
                            </div>
                        </div>
                    </div>
                </div>

                }

                @if (isEditMode()) {
         
                <form [formGroup]="editGroupForm" (ngSubmit)="onSubmit()">
                    <!-- Edit form fields -->
                    <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        Group Name
                    </h3>
                    <app-prime-reactive-form-input [showLabelAbove]="true" formControlName="groupName"
                        [parentForm]="editGroupForm" placeholder="Enter group name" [required]="false"
                        data-name="groupName" pAutoFocus [inputClass]="'w-full'" />


                    <!-- <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        Student Level <span class="text-red-600">*</span>
                    </h3>
                    <p-select formControlName="studentLevel" dataKey="code" optionValue="code" [options]="studentLevels"
                        appendTo="body" optionLabel="name" placeholder="Select student level" styleClass="w-full">
                    </p-select>
                    <app-form-field-validation-message messageClass="text-red-500 mb-0"
                        [control]="editGroupForm.controls['studentLevel']!" propertyName="student level" /> -->



                    <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        Select Students <span class="text-red-600">*</span>
                    </h3>
                    <p class="text-600 text-sm mb-3 line-height-3">
                        Choose students with compatible language levels for the group.
                    </p>

                    <app-smart-student-selector [students]="availableStudentsForLanguage$()" [selectedStudents]="selectedStudents()"
                        [selectedLanguageName]="getSelectedLanguageName()"
                        [computedGroupLanguageLevels]="computedGroupLanguageLevels()"
                        [computedGroupLevelsText]="computedGroupLevelsText()"
                        [loading]="services.dataState.parentStudents.state().loading" [isEditMode]="true"
                        (studentSelected)="onStudentSelected($event)">
                    </app-smart-student-selector>

                    @if (availabilityData() && availabilityDataLoaded()) {


                    <div class="mt-2">
                        @if (!areAllCommonTimeSlotsEmptyBool()) {

                        <div class="mt-3 flex flex-column md:flex-row align-items-center justify-content-between gap-3 px-3 py-2 border-round-xl border-1 mb-3 bg-green-50/50 border-green-300 backdrop-blur-sm shadow-1"
                            *ngIf="!isPanelClosed">
                            <div class="flex align-items-center gap-2">
                                <i class="pi pi-check-circle text-green-700 text-xl"></i>
                                <p class="text-green-700 text-sm m-0 line-height-3">
                                    We found some common timeslots for your students. Please review them
                                    carefully.
                                </p>
                            </div>
                            <p-button label="OK" icon="pi pi-check" severity="success" size="small"
                                (click)="isPanelClosed = true">
                            </p-button>
                        </div>
                        }

                        <app-availability-picker-days [showTimezoneDisplay]="false"
                            [noteText]="'Timezone:' + availabilityTimezone.timeZoneDisplayName"
                            [availabilityData]="availabilityData()" (timeSlotsChange)="onTimeSlotsChange($event)"
                            (availabilityFormValid)="onAvailabilityDaysFormValid($event)">
                            @if (this.editGroupState() === GroupDialogState.EditGroup) {
                            <app-availability-timezone-selector
                                [userBasicInfo]="services.auth.getUserProfileInfo()!.basicProfileInfoDto!"
                                [availabilityTimezone]="availabilityTimezone"
                                (timezoneChanged)="onTimeZoneChanged($event)"></app-availability-timezone-selector>
                            }
                        </app-availability-picker-days>


                    </div>
                    } @else {

                    <div
                        class="mt-1 flex flex-column md:flex-row align-items-center justify-content-between
                                         gap-3 px-3 py-3 border-round-lg border-1 mb-3 bg-blue-50/70 border-blue-300 backdrop-blur-sm shadow-2">
                        <div class="flex align-items-center gap-3">
                            <i class="pi pi-users text-blue-700 text-2xl"></i>
                            <div>
                                <p class="text-blue-700 text-sm font-medium m-0 line-height-3">
                                    Student Selection Required
                                </p>
                                <p class="text-blue-600 text-xs m-0 line-height-3 mt-1">
                                    To set up availability time slots, please choose your students first.
                                </p>
                            </div>
                        </div>
                    </div>
                    }


                    <!-- <h3
                        class="align-items-center line-height-1 text-sm font-semibold mb-1 mt-3 primary-purple-color flex">
                        More Details
                    </h3>
                    <textarea pTextarea [autoResize]="true" class="w-full h-9rem mb-3" cols="35"
                        formControlName="moreDetails"
                        placeholder="Tell us more about the students, their current level, schedule and goals they want to achieve by creating this group."
                        rows="5"></textarea> -->


                    <div class="flex flex-column align-items-center justify-content-center">
                        <p-button type="submit" icon="pi pi-check-circle" iconPos="left" label="Update Group"
                            [loading]="services.apiLoadingStateService.getIsLoading()"
                            [styleClass]="canCreateGroup() ? 'bg-indigo-600 mt-2' : 'mt-2'"
                            [severity]="canCreateGroup() ? 'primary' : 'secondary'"></p-button>
                    </div>
                </form>

                }


                }


            </div>
        </div>

        }
    </div>
</div>