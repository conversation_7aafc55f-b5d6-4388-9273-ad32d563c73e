
 <!-- {{this.myForm.value | json}} -->
<div class="surface-section border-bottom-1 border-gray-200 border-round">

    <!-- valid: {{this.myForm.valid}} -->
    <div class="hidden" [ngClass]="{'flex pt-0 gap-5 flex-column-reverse md:flex-row': currentStep === 1}">
        <div class="flex-auto p-fluid">
            <form autocomplete="off" [formGroup]="myForm" (ngSubmit)="submitPassword()">
                <app-password-strength-indicator [password]="myForm.get('password')?.value || ''"></app-password-strength-indicator>
                <div class="surface-section w-full flex flex-column align-items-start justify-content-center">
                    <div class="field w-full">
                        <div class=" w-full">
                            <label htmlFor="newPassword" class="text-900 font-medium mb-2 block">New Password</label>
                            <p-password formControlName="password" #newPwdInput [feedback]="false" [toggleMask]="true"
                                styleClass="w-full" id="new-password" inputStyleClass="w-full shadow-none text-left"
                                 autocomplete="password"></p-password>
                            <app-form-field-validation-message messageClass="text-red-500 mb-1"
                                [control]="myForm.controls['password']"></app-form-field-validation-message>
                        </div>
                    </div>
                    <div class="field w-full">
                        <div class="w-full">
                            <label htmlFor="confirmPassword" class="text-900 font-medium mb-2 block">Verify
                                Password</label>
                            <p-password formControlName="confirmPassword" [feedback]="false" [toggleMask]="true"
                                styleClass="w-full" inputStyleClass="w-full shadow-none text-left" autocomplete="off"></p-password>
                            <div *ngIf="myForm.get('confirmPassword')?.errors && myForm.get('confirmPassword')?.touched"
                                class="text-red-500 text-xs">
                                <app-form-field-validation-message messageClass="text-red-500 mb-3"
                                    [text]="'Passwords do not match'"></app-form-field-validation-message>
                            </div>
                        </div>
                    </div>
                </div>
  
                <div class="flex gap-5 flex-column-reverse md:flex-row">
                    <div class="flex-auto p-fluid">
                        <div id="invalid-controls">
                            <app-form-field-validation-message *ngIf="invalidFields.length > 0 && submitted()" [severity]="Severity.Warning" styleClass="mb-2"
                              messageClass="mb-2"
                              [text]="'Please complete all mandatory fields in the form.'"></app-form-field-validation-message>
                        
                            <div *ngIf="invalidFields.length > 0 && submitted()" class="invalid-fields-message">
                        
                              <p>The following fields are invalid:</p>
                        
                              <ul class="mt-1 list-none p-0 mx-0">
                                <li *ngFor="let field of invalidFields" class="flex align-items-center py-1">
                                  <span class="border-round bg-red-500 mr-3 flex-shrink-0" style="width: 0.725rem; height: 0.725rem;"></span>
                                  <span class="text-sm font-medium text-90">{{ field }}</span>
                        
                                </li>
                              </ul>
                              <hr>
                            </div>
                          </div>
                    </div>
                </div>

                <div class="flex mt-2 w-full align-items-center justify-content-center">
                    <p-button type="submit" class="w-full" styleClass="btn-standard btn-primary btn-block" label="Save Password"
                        icon="pi pi-check" iconPos="right"></p-button>
                </div>

       
            </form>
        </div>
    </div>

    <!-- <app-actionable-alert iconUrl="/assets/images/graphic/lock_security_icon.svg" title="Verify Password Change"
            message="We’ve sent a verification code to your email. Enter it below to confirm your new password."
            alertClass="bg-cyan-50" imageClass="''" alertTextHeaderClass="" alertTextSubHeaderClass="">
        </app-actionable-alert>
    -->
  
</div>